/**
 * Scope Gate - Third gate in the cascade flow
 *
 * Detects when user input has scope issues like too many features or kitchen-sink approach.
 * Fails when feature lists extend beyond a minimal lovable product (MLP).
 */

import { v } from "convex/values";
import { mutation } from "../../_generated/server";
import type { MutationCtx } from "../../_generated/server";
import type {
  DetailedGateResult,
  GateContext,
  GateFailureReason
} from "../sharedTypes";

/**
 * Pattern configuration for scope issue detection
 */
type ScopePatternConfig = {
  pattern: RegExp;
  reason: GateFailureReason;
  weight: number;
  threshold?: number; // Optional threshold for count-based patterns
};

/**
 * Patterns that indicate scope issues
 */
const SCOPE_ISSUE_PATTERNS: Record<string, ScopePatternConfig> = {
  // Feature-related keywords (count will be checked programmatically)
  featureKeywords: {
    pattern: /\b(?:feature|function|capability|component|module|section|page|screen|tab)\b/gi,
    reason: "too_many_features" as GateFailureReason,
    weight: 0.8,
    threshold: 5 // Fail if 5+ feature keywords found
  },

  // Multiple core functions (count-based detection)
  multipleCoreFunction: {
    pattern: /\b(core|main|primary|key|essential|critical)\s+(feature|function|functionality|capability)\b/gi,
    reason: "multiple_core_functions" as GateFailureReason,
    weight: 0.9,
    threshold: 2 // Fail if 2+ "core/main/primary" functions mentioned
  },

  // Ecosystem-scale language
  ecosystemScale: {
    pattern: /\b(platform|ecosystem|marketplace|app store|plugin system|api for others|third[- ]party|integration|sdk|developer tools|white[- ]label)\b/gi,
    reason: "ecosystem_scale_detected" as GateFailureReason,
    weight: 0.7
  },

  // Kitchen sink indicators
  kitchenSink: {
    pattern: /\b(everything|all|every|complete|comprehensive|full[- ]featured|end[- ]to[- ]end|one[- ]stop|all[- ]in[- ]one)\b/gi,
    reason: "kitchen_sink_approach" as GateFailureReason,
    weight: 0.6
  },

  // Scope creep language
  scopeCreep: {
    pattern: /\b(also|additionally|plus|furthermore|moreover|and also|as well as|not only.*but also|while we're at it)\b/gi,
    reason: "scope_creep" as GateFailureReason,
    weight: 0.5
  }
};

/**
 * Analyzes user input for scope issues
 */
function analyzeScope(userInput: string): {
  hasAppropriateScope: boolean;
  failureReasons: GateFailureReason[];
  confidence: number;
  detectedPatterns: Array<{ pattern: string; snippet: string; reason: GateFailureReason }>;
  featureCount: number;
  identifiedFeatures: string[];
} {
  const detectedPatterns: Array<{ pattern: string; snippet: string; reason: GateFailureReason }> = [];
  let maxWeight = 0;

  // Check each pattern
  for (const [patternName, config] of Object.entries(SCOPE_ISSUE_PATTERNS)) {
    const matches = userInput.match(config.pattern);
    if (matches) {
      // Special handling for count-based patterns
      if (config.threshold) {
        if (matches.length >= config.threshold) {
          const snippetText = patternName === 'featureKeywords'
            ? `${matches.length} feature keywords detected: ${matches.slice(0, 3).join(', ')}${matches.length > 3 ? '...' : ''}`
            : `${matches.length} instances of "${config.reason.replace(/_/g, ' ')}" detected: ${matches.slice(0, 2).join(', ')}${matches.length > 2 ? '...' : ''}`;

          detectedPatterns.push({
            pattern: patternName,
            snippet: snippetText,
            reason: config.reason
          });
          maxWeight = Math.max(maxWeight, config.weight);
        }
      } else {
        // Regular pattern matching for other patterns
        for (const match of matches) {
          detectedPatterns.push({
            pattern: patternName,
            snippet: match,
            reason: config.reason
          });
          maxWeight = Math.max(maxWeight, config.weight);
        }
      }
    }
  }

  // Extract and count features
  const features = extractFeatures(userInput);
  const featureCount = features.length;

  // Additional scope checks based on feature count
  if (featureCount > 5) {
    detectedPatterns.push({
      pattern: "feature_count_exceeded",
      snippet: `${featureCount} features detected`,
      reason: "too_many_features"
    });
    maxWeight = Math.max(maxWeight, 0.8);
  }

  // Calculate confidence and determine if scope is appropriate
  const confidence = maxWeight;
  const hasAppropriateScope = confidence < 0.5 && featureCount <= 5;

  const failureReasons = [...new Set(detectedPatterns.map(p => p.reason))];

  return {
    hasAppropriateScope,
    failureReasons,
    confidence,
    detectedPatterns,
    featureCount,
    identifiedFeatures: features
  };
}

/**
 * Extracts potential features from user input
 */
function extractFeatures(userInput: string): string[] {
  const featurePatterns = [
    /\b(?:user|users can|ability to|feature to|function to|can)\s+([^.!?]+)/gi,
    /\b(?:login|signup|register|authentication|auth|sign[- ]in|sign[- ]up)\b/gi,
    /\b(?:dashboard|profile|settings|preferences|account|admin panel)\b/gi,
    /\b(?:search|filter|sort|browse|navigation|menu)\b/gi,
    /\b(?:upload|download|share|export|import|sync)\b/gi,
    /\b(?:notification|alert|email|sms|push notification)\b/gi,
    /\b(?:payment|billing|subscription|checkout|cart|purchase)\b/gi,
    /\b(?:report|analytics|metrics|statistics|tracking)\b/gi,
    /\b(?:chat|messaging|comment|review|rating|feedback)\b/gi,
    /\b(?:calendar|schedule|booking|appointment|event)\b/gi
  ];

  const features: string[] = [];

  for (const pattern of featurePatterns) {
    const matches = userInput.match(pattern);
    if (matches) {
      features.push(...matches.map(match => match.toLowerCase().trim()));
    }
  }

  // Remove duplicates and return
  return [...new Set(features)];
}

/**
 * Scope-specific failure reasons used by this gate
 */
type ScopeGateFailureReason =
  | "too_many_features"
  | "multiple_core_functions"
  | "ecosystem_scale_detected"
  | "kitchen_sink_approach"
  | "scope_creep";

/**
 * Generates scope limitation message
 */
function generateScopeLimitationMessage(
  detectedPatterns: Array<{ pattern: string; snippet: string; reason: GateFailureReason }>,
  featureCount: number,
  identifiedFeatures: string[]
): string {
  const reasonMessages: Record<ScopeGateFailureReason, string> = {
    too_many_features: `I count ${featureCount} different features in your request. For a successful first version, let's focus on 3-5 core features.`,
    multiple_core_functions: "You've mentioned multiple 'core' or 'main' functions. A focused product should have one primary purpose.",
    ecosystem_scale_detected: "Your request sounds like a platform or ecosystem, which is quite ambitious. Let's start with a simpler, focused product first.",
    kitchen_sink_approach: "It sounds like you want to include everything possible. Let's narrow down to the most essential features for your users.",
    scope_creep: "I notice the scope keeps expanding. Let's define clear boundaries for the first version."
  };

  // Get the primary reason and ensure it's a scope-specific reason
  const primaryReason = detectedPatterns[0]?.reason;
  const baseMessage = primaryReason && primaryReason in reasonMessages
    ? reasonMessages[primaryReason as ScopeGateFailureReason]
    : "Your request has scope issues.";

  let suggestion = "\n\nLet's create a Minimal Lovable Product (MLP) instead. ";

  if (identifiedFeatures.length > 3) {
    const topFeatures = identifiedFeatures.slice(0, 3);
    suggestion += `From what you've described, the top 3 features might be: ${topFeatures.join(", ")}. `;
  }

  suggestion += "Which ONE core problem are you trying to solve for your users?";

  return baseMessage + suggestion;
}

/**
 * Scope Gate Convex mutation
 *
 * Analyzes user input for scope issues and returns pass/fail result with scope limitation guidance.
 * Converts to proper Convex mutation as specified in task requirements.
 */
export const scopeGate = mutation({
  args: {
    conversationId: v.id("conversations"),
    message: v.string()
  },
  handler: async (ctx, { conversationId, message }): Promise<DetailedGateResult> => {
    const startTime = Date.now();

    try {
      // Validate conversation exists
      const conversation = await ctx.db.get(conversationId);
      if (!conversation) {
        throw new Error("Conversation not found");
      }

      // Analyze the user input for scope issues
      const analysis = analyzeScope(message);

      const processingTime = Date.now() - startTime;

      if (!analysis.hasAppropriateScope) {
        // Generate scope limitation message
        const scopeLimitationMessage = generateScopeLimitationMessage(
          analysis.detectedPatterns,
          analysis.featureCount,
          analysis.identifiedFeatures
        );

        // Store gate result in database
        await ctx.db.insert("gate_results", {
          conversationId,
          gate: "scope",
          status: "fail",
          reasons: analysis.failureReasons,
          createdAt: Date.now()
        });

        return {
          status: "fail",
          message: scopeLimitationMessage,
          failureReasons: analysis.failureReasons,
          confidence: analysis.confidence,
          processingTime,
          metadata: {
            detectedPatterns: analysis.detectedPatterns.length,
            featureCount: analysis.featureCount,
            primaryReason: analysis.failureReasons[0] || "scope_issue"
          }
        };
      } else {
        // Store successful gate result
        await ctx.db.insert("gate_results", {
          conversationId,
          gate: "scope",
          status: "pass",
          reasons: [], // Empty array for successful gates
          createdAt: Date.now()
        });

        return {
          status: "pass",
          message: "Request has appropriate scope for an MLP.",
          confidence: 1 - analysis.confidence, // Invert confidence for pass case
          processingTime,
          metadata: {
            detectedPatterns: 0,
            featureCount: analysis.featureCount,
            primaryReason: "appropriate_scope"
          }
        };
      }
    } catch (error) {
      // Log error and check if it's a DB write failure
      console.error("Scope gate error:", error);

      // If it's a database error, throw specific error for pipeline runner
      if (error instanceof Error && error.message.includes("insert")) {
        throw new Error("DB_WRITE_FAILED");
      }

      return {
        status: "fail",
        message: "Internal error occurred while analyzing your request scope. Please try again.",
        confidence: 0,
        processingTime: Date.now() - startTime,
        metadata: {
          error: "internal_error"
        }
      };
    }
  }
});

/**
 * Internal helper function for use within the cascade workflow
 * Maintains backward compatibility with existing cascade workflow
 */
export async function scopeGateInternal(
  ctx: MutationCtx,
  context: GateContext
): Promise<DetailedGateResult> {
    const startTime = Date.now();

    try {
      // Validate conversation exists
      const conversation = await ctx.db.get(context.conversationId);
      if (!conversation) {
        throw new Error("Conversation not found");
      }

      // Analyze the user input for scope issues
      const analysis = analyzeScope(context.userInput);

      const processingTime = Date.now() - startTime;

      if (!analysis.hasAppropriateScope) {
        // Generate scope limitation message
        const scopeLimitationMessage = generateScopeLimitationMessage(
          analysis.detectedPatterns,
          analysis.featureCount,
          analysis.identifiedFeatures
        );

        // Store gate result in database
        await ctx.db.insert("gate_results", {
          conversationId: context.conversationId,
          gate: "scope",
          status: "fail",
          reasons: analysis.failureReasons,
          createdAt: Date.now()
        });

        return {
          status: "fail",
          message: scopeLimitationMessage,
          failureReasons: analysis.failureReasons,
          confidence: analysis.confidence,
          processingTime,
          metadata: {
            detectedPatterns: analysis.detectedPatterns.length,
            featureCount: analysis.featureCount,
            primaryReason: analysis.failureReasons[0] || "scope_issue"
          }
        };
      } else {
        // Store successful gate result
        await ctx.db.insert("gate_results", {
          conversationId: context.conversationId,
          gate: "scope",
          status: "pass",
          reasons: [], // Empty array for successful gates
          createdAt: Date.now()
        });

        return {
          status: "pass",
          message: "Request has appropriate scope for an MLP.",
          confidence: 1 - analysis.confidence, // Invert confidence for pass case
          processingTime,
          metadata: {
            detectedPatterns: 0,
            featureCount: analysis.featureCount,
            primaryReason: "appropriate_scope"
          }
        };
      }
    } catch (error) {
      // Log error and check if it's a DB write failure
      console.error("Scope gate error:", error);

      // If it's a database error, throw specific error for pipeline runner
      if (error instanceof Error && error.message.includes("insert")) {
        throw new Error("DB_WRITE_FAILED");
      }

      return {
        status: "fail",
        message: "Internal error occurred while analyzing your request scope. Please try again.",
        confidence: 0,
        processingTime: Date.now() - startTime,
        metadata: {
          error: "internal_error"
        }
      };
    }
}
